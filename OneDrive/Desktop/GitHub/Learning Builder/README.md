# 📚 AI Tutor Platform

AI платформа за автоматична обработка на документи и създаване на обучителни материали - флашкарти, въпроси с избираем отговор и обяснения на термини.

## 🚀 Функционалности

- **Качване на документи**: PDF, DOCX, TXT файлове
- **AI обработка**: Автоматично генериране на обучителни материали
- **Флашкарти**: Въпрос/отговор формат за учене
- **Тестове**: Въпроси с избираем отговор
- **Обяснения**: Дефиниции на ключови термини
- **Експорт**: PDF, CSV, Anki формати

## 🏗️ Архитектура

- **Backend**: Django + DRF + Celery + PostgreSQL + Redis
- **Frontend**: React + TypeScript + Vite
- **AI**: OpenAI GPT-3.5-turbo
- **Deployment**: Docker ready

## 📋 Изисквания

- Python 3.8+
- Node.js 18+
- PostgreSQL 12+
- Redis 6+
- OpenAI API ключ

## 🛠️ Инсталация

### 1. Клониране на проекта
```bash
git clone <repository-url>
cd Learning\ Builder
```

### 2. Backend Setup

```bash
# Създаване на виртуална среда
python -m venv venv
source venv/bin/activate  # Linux/Mac
# или
venv\Scripts\activate  # Windows

# Инсталиране на зависимости
pip install -r requirements.txt

# Копиране на environment файла
cp .env.example .env
# Редактирай .env файла с твоите настройки

# Миграции на базата данни
python manage.py makemigrations
python manage.py migrate

# Стартиране на Django сървъра
python manage.py runserver
```

### 3. Celery Worker (в отделен терминал)
```bash
celery -A Learning_Builder worker --loglevel=info
```

### 4. Frontend Setup

```bash
cd ai-tutor-frontend

# Инсталиране на зависимости
npm install

# Стартиране на development сървъра
npm run dev
```

## 🔧 Конфигурация

### Environment Variables (.env)

```env
SECRET_KEY=your-secret-key-here
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1

DB_NAME=learning_builder_db
DB_USER=postgres-user
DB_PASSWORD=password
DB_HOST=127.0.0.1
DB_PORT=5432

CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

OPENAI_API_KEY=your-openai-api-key-here
```

### PostgreSQL Setup

```sql
CREATE DATABASE learning_builder_db;
CREATE USER postgres_user WITH PASSWORD 'password';
GRANT ALL PRIVILEGES ON DATABASE learning_builder_db TO postgres_user;
```

## 📖 API Документация

API документацията е достъпна на:
- Swagger UI: `http://localhost:8000/api/docs/`
- OpenAPI Schema: `http://localhost:8000/api/schema/`

### Основни Endpoints

- `POST /api/documents/upload/` - Качване на документ
- `GET /api/documents/status/{id}/` - Статус на документ
- `GET /api/documents/{id}/questions/` - Въпроси за документ
- `GET /api/documents/{id}/flashcards/` - Флашкарти за документ
- `GET /api/documents/{id}/explanations/` - Обяснения за документ
- `POST /api/documents/{id}/generate/questions/` - Генериране на въпроси
- `POST /api/documents/{id}/generate/flashcards/` - Генериране на флашкарти
- `POST /api/documents/{id}/generate/explanations/` - Генериране на обяснения

## 🧪 Тестване

```bash
# Backend тестове
python manage.py test

# Frontend тестове
cd ai-tutor-frontend
npm test
```

## 🐳 Docker Deployment

```bash
# Стартиране с Docker Compose
docker-compose up -d
```

## 🔍 Известни проблеми

1. **OpenAI API Rate Limits**: Внимавайте с честотата на заявките
2. **Large Files**: Файлове над 10MB могат да причинят проблеми
3. **Cyrillic Support**: PDF файлове с кирилица могат да имат проблеми с извличането на текст

## 🤝 Принос

1. Fork проекта
2. Създайте feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit промените (`git commit -m 'Add some AmazingFeature'`)
4. Push към branch (`git push origin feature/AmazingFeature`)
5. Отворете Pull Request

## 📄 Лиценз

Този проект е лицензиран под MIT License.

## 📞 Поддръжка

За въпроси и поддръжка, моля отворете issue в GitHub repository.
