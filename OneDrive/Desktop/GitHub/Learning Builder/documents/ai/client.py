import os
import logging

try:
    import openai
except ImportError:
    openai = None

logger = logging.getLogger(__name__)

class OpenAIClient:
    """
    Клиент за работа с OpenAI API. Използва се за генериране на completion по подаден prompt.
    """
    def __init__(self):
        self.api_key = os.environ.get('OPENAI_API_KEY')
        if not openai:
            raise ImportError('openai пакетът не е инсталиран')
        if not self.api_key:
            raise ValueError('Липсва OPENAI_API_KEY')
        self.client = openai.OpenAI(api_key=self.api_key)

    def generate_completion(self, prompt: str, content_type: str = None, use_cache: bool = True) -> str:
        """
        Извиква OpenAI API с дадения prompt и връща резултата като string.
        Използва кеширане за намаляване на API заявките.

        Args:
            prompt: Prompt за OpenAI
            content_type: Тип съдържание за кеширане ('questions', 'flashcards', 'explanations', 'summary')
            use_cache: Дали да използва кеширане
        """
        from documents.utils.cache import AIResultCache

        # Проверка за кеширан резултат
        if use_cache and content_type:
            cached_result = AIResultCache.get_cached_result(content_type, prompt)
            if cached_result:
                logger.info(f"Using cached result for {content_type}")
                return cached_result

        try:
            logger.info(f"Making OpenAI API call for {content_type or 'unknown'}")
            response = self.client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": "Ти си полезен AI тютор, който създава качествени обучителни материали."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=4096,
                temperature=0.7,
            )
            result = response.choices[0].message.content.strip()

            # Кеширане на резултата
            if use_cache and content_type:
                AIResultCache.cache_result(content_type, prompt, result)

            logger.info(f"Successfully generated {content_type or 'content'} with {len(result)} characters")
            return result

        except Exception as e:
            logger.error(f"OpenAI API error for {content_type}: {e}")
            raise RuntimeError(f"OpenAI error: {e}")

def call_openai(prompt: str) -> str:
    """
    Съвместим wrapper за стария интерфейс. Използва OpenAIClient.
    """
    try:
        client = OpenAIClient()
        return client.generate_completion(prompt)
    except Exception as e:
        return f"[OpenAI error: {e}]"
