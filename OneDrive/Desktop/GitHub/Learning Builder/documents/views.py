from django.shortcuts import render
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from .models import UploadedDocument
from .serializers import UploadedDocumentSerializer
from .tasks import process_uploaded_document
from rest_framework.generics import RetrieveAPIView, ListAPIView
from .serializers import QuestionSerializer, FlashcardSerializer, ExplanationSerializer
from .models import Question, Flashcard, Explanation
from django.http import FileResponse, Http404
from django_ratelimit.decorators import ratelimit
from django.utils.decorators import method_decorator
import io
import csv
try:
    from reportlab.lib.pagesizes import letter
    from reportlab.pdfgen import canvas
except ImportError:
    canvas = None
try:
    import genanki
except ImportError:
    genanki = None
from documents.ai.client import OpenAIClient
from documents.ai.prompts import generate_questions_prompt, generate_flashcards_prompt, generate_explanations_prompt
from documents.utils.text_extraction import extract_text
from .tasks import parse_questions, parse_flashcards, parse_explanations

# Create your views here.

@method_decorator(ratelimit(key='ip', rate='10/h', method='POST', block=True), name='post')
class DocumentUploadView(APIView):
    """
    API endpoint за качване на документ. Връща ID и статус.
    """
    def post(self, request, *args, **kwargs):
        serializer = UploadedDocumentSerializer(data=request.data)
        if serializer.is_valid():
            document = serializer.save(status='ready')  # НЕ стартирай process_uploaded_document
            return Response({
                'id': str(document.id),
                'status': document.status
            }, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def get(self, request, *args, **kwargs):
        """
        Връща информация за документ по id (чрез ?id=...)
        """
        document_id = request.query_params.get('id')
        if not document_id:
            return Response({'detail': 'Missing id parameter.'}, status=status.HTTP_400_BAD_REQUEST)
        try:
            doc = UploadedDocument.objects.get(id=document_id)
            serializer = UploadedDocumentSerializer(doc)
            return Response(serializer.data)
        except UploadedDocument.DoesNotExist:
            return Response({'detail': 'Document not found.'}, status=status.HTTP_404_NOT_FOUND)

class DocumentStatusView(RetrieveAPIView):
    """
    Връща статус и детайли за документ по ID (pk) – удобен endpoint за клиентите.
    """
    queryset = UploadedDocument.objects.all()
    serializer_class = UploadedDocumentSerializer

class DocumentQuestionsView(ListAPIView):
    """
    Връща списък с въпроси за даден документ (по document_id).
    """
    serializer_class = QuestionSerializer
    def get_queryset(self):
        return Question.objects.filter(document_id=self.kwargs['document_id'])

class DocumentFlashcardsView(ListAPIView):
    """
    Връща списък с флашкарти за даден документ (по document_id).
    """
    serializer_class = FlashcardSerializer
    def get_queryset(self):
        return Flashcard.objects.filter(document_id=self.kwargs['document_id'])

class DocumentExplanationsView(ListAPIView):
    """
    Връща списък с обяснения/термини за даден документ (по document_id).
    """
    serializer_class = ExplanationSerializer
    def get_queryset(self):
        return Explanation.objects.filter(document_id=self.kwargs['document_id'])

class DocumentExportPDFView(APIView):
    """
    Генерира PDF с въпросите за даден документ.
    """
    def get(self, request, document_id):
        if not canvas:
            return Response({'detail': 'reportlab не е инсталиран.'}, status=500)
        try:
            doc = UploadedDocument.objects.get(id=document_id)
        except UploadedDocument.DoesNotExist:
            raise Http404('Document not found')
        questions = Question.objects.filter(document=doc)
        if not questions.exists():
            return Response({'detail': 'Няма въпроси за този документ.'}, status=404)
        buffer = io.BytesIO()
        p = canvas.Canvas(buffer, pagesize=letter)
        width, height = letter
        y = height - 50
        # Регистрация на DejaVuSans за кирилица
        try:
            from reportlab.pdfbase.ttfonts import TTFont
            from reportlab.pdfbase import pdfmetrics
            import os
            font_path = os.path.join(os.path.dirname(__file__), 'DejaVuSans.ttf')
            if not os.path.exists(font_path):
                # fallback: търси в системните шрифтове
                font_path = '/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf'
            pdfmetrics.registerFont(TTFont('DejaVuSans', font_path))
            font_name = 'DejaVuSans'
        except Exception as e:
            print('Неуспешно зареждане на DejaVuSans:', e)
            font_name = 'Helvetica'
        p.setFont(font_name, 16)
        p.drawString(50, y, f'Въпроси за документ: {doc.title or doc.id}')
        y -= 40
        p.setFont(font_name, 12)
        for idx, q in enumerate(questions, 1):
            if y < 100:
                p.showPage()
                y = height - 50
                p.setFont(font_name, 12)
            p.drawString(50, y, f'{idx}. {q.question_text}')
            y -= 20
            for cidx, choice in enumerate(q.choices, 1):
                p.drawString(70, y, f'   {chr(64+cidx)}. {choice}')
                y -= 15
            if q.explanation:
                p.drawString(70, y, f'Обяснение: {q.explanation}')
                y -= 15
            y -= 10
        p.save()
        buffer.seek(0)
        return FileResponse(buffer, as_attachment=True, filename=f'questions_{doc.id}.pdf')

class DocumentExportAnkiView(APIView):
    """
    Генерира Anki .apkg файл с флашкартите за даден документ.
    """
    def get(self, request, document_id):
        if not genanki:
            return Response({'detail': 'genanki не е инсталиран.'}, status=500)
        try:
            doc = UploadedDocument.objects.get(id=document_id)
        except UploadedDocument.DoesNotExist:
            raise Http404('Document not found')
        flashcards = Flashcard.objects.filter(document=doc)
        if not flashcards.exists():
            return Response({'detail': 'Няма флашкарти за този документ.'}, status=404)
        deck = genanki.Deck(
            deck_id=1234567890 + int(doc.id.int % 1e9),
            name=f'AI Tutor Export: {doc.title or doc.id}'
        )
        model = genanki.Model(
            1607392319,
            'Simple Model',
            fields=[{'name': 'Front'}, {'name': 'Back'}],
            templates=[{
                'name': 'Card 1',
                'qfmt': '{{Front}}',
                'afmt': '{{Front}}<hr id="answer">{{Back}}',
            }],
        )
        for f in flashcards:
            note = genanki.Note(
                model=model,
                fields=[f.front, f.back]
            )
            deck.add_note(note)
        package = genanki.Package(deck)
        buffer = io.BytesIO()
        package.write_to_file(buffer)
        buffer.seek(0)
        return FileResponse(buffer, as_attachment=True, filename=f'flashcards_{doc.id}.apkg')

class DocumentExportCSVView(APIView):
    """
    Генерира CSV с въпросите за даден документ.
    """
    def get(self, request, document_id):
        try:
            doc = UploadedDocument.objects.get(id=document_id)
        except UploadedDocument.DoesNotExist:
            raise Http404('Document not found')
        questions = Question.objects.filter(document=doc)
        if not questions.exists():
            return Response({'detail': 'Няма въпроси за този документ.'}, status=404)
        buffer = io.StringIO()
        # Добавям BOM за UTF-8
        buffer.write('\ufeff')
        writer = csv.writer(buffer)
        writer.writerow(['question', 'choice_1', 'choice_2', 'choice_3', 'choice_4', 'correct_choice', 'explanation'])
        for q in questions:
            choices = q.choices if isinstance(q.choices, list) else []
            row = [q.question_text]
            for i in range(4):
                row.append(choices[i] if i < len(choices) else '')
            row.append(q.correct_choice)
            row.append(q.explanation or '')
            writer.writerow(row)
        buffer.seek(0)
        response = FileResponse(io.BytesIO(buffer.getvalue().encode('utf-8')), as_attachment=True, filename=f'questions_{doc.id}.csv')
        response['Content-Type'] = 'text/csv; charset=utf-8'
        return response

@method_decorator(ratelimit(key='ip', rate='5/h', method='POST', block=True), name='post')
class GenerateQuestionsView(APIView):
    def post(self, request, document_id):
        try:
            from .ai.client import OpenAIClient
            from .ai.prompts import generate_questions_prompt
            from .tasks import parse_questions
            from .models import UploadedDocument, Question
            from documents.utils.text_extraction import extract_text

            doc = UploadedDocument.objects.get(id=document_id)
            file_path = doc.file.path
            extracted_text = extract_text(file_path)
            print('Extracted text for questions:', extracted_text[:500])

            client = OpenAIClient()
            questions_ai = client.generate_completion(generate_questions_prompt(extracted_text), content_type='questions')
            print('AI questions output (view):', questions_ai)

            doc.questions.all().delete()
            parsed = parse_questions(questions_ai)
            print('Parsed questions (view):', parsed)

            for q in parsed:
                Question.objects.create(document=doc, **q)
            return Response({'status': 'ok'})
        except UploadedDocument.DoesNotExist:
            return Response({'error': 'Документът не е намерен'}, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@method_decorator(ratelimit(key='ip', rate='5/h', method='POST', block=True), name='post')
class GenerateFlashcardsView(APIView):
    def post(self, request, document_id):
        try:
            doc = UploadedDocument.objects.get(id=document_id)
            file_path = doc.file.path
            extracted_text = extract_text(file_path)
            print('Extracted text:', extracted_text[:500])
            client = OpenAIClient()
            flashcards_ai = client.generate_completion(generate_flashcards_prompt(extracted_text), content_type='flashcards')
            print('AI flashcards output:', flashcards_ai)
            # Изтрий старите флашкарти
            doc.flashcards.all().delete()
            for f in parse_flashcards(flashcards_ai):
                Flashcard.objects.create(document=doc, **f)
            doc.status = 'ready'
            doc.save()
            return Response({'status': 'ready'})
        except Exception as e:
            doc.status = 'failed'
            doc.save()
            return Response({'status': 'failed', 'error': str(e)}, status=500)

@method_decorator(ratelimit(key='ip', rate='5/h', method='POST', block=True), name='post')
class GenerateExplanationsView(APIView):
    def post(self, request, document_id):
        try:
            from .ai.client import OpenAIClient
            from .ai.prompts import generate_explanations_prompt
            from .tasks import parse_explanations
            from .models import UploadedDocument, Explanation
            from documents.utils.text_extraction import extract_text

            doc = UploadedDocument.objects.get(id=document_id)
            file_path = doc.file.path
            extracted_text = extract_text(file_path)
            print('Extracted text for explanations:', extracted_text[:500])

            client = OpenAIClient()
            explanations_ai = client.generate_completion(generate_explanations_prompt(extracted_text), content_type='explanations')
            print('AI explanations output (view):', explanations_ai)

            doc.explanations.all().delete()
            parsed = parse_explanations(explanations_ai)
            print('Parsed explanations (view):', parsed)

            for e in parsed:
                Explanation.objects.create(document=doc, **e)
            return Response({'status': 'ok'})
        except UploadedDocument.DoesNotExist:
            return Response({'error': 'Документът не е намерен'}, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
