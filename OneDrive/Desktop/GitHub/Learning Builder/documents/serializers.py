from rest_framework import serializers
# Временно коментирано за миграция
# from .models import UploadedDocument, Question, Flashcard, Explanation
from django.core.exceptions import ValidationError
from drf_spectacular.utils import extend_schema_serializer, OpenApiExample


def validate_file_extension(value):
    """
    Валидира, че файлът е с разширение pdf, docx или txt.
    """
    allowed = ('.pdf', '.docx', '.txt')
    if not value.name.lower().endswith(allowed):
        raise ValidationError("Unsupported file type. Позволени са само PDF, DOCX, TXT.")

def validate_file_size(value):
    """
    Валидира размера на файла - максимум 50MB.
    """
    max_size = 50 * 1024 * 1024  # 50MB в байтове
    if value.size > max_size:
        raise ValidationError(f"Файлът е твърде голям. Максимален размер: 50MB. Вашият файл: {value.size / (1024*1024):.1f}MB")

def validate_file_content(value):
    """
    Основна валидация на съдържанието на файла.
    """
    # Проверка за минимален размер
    min_size = 100  # 100 байта минимум
    if value.size < min_size:
        raise ValidationError("Файлът е твърде малък за обработка.")

    # Проверка за подозрителни файлове
    dangerous_extensions = ['.exe', '.bat', '.cmd', '.scr', '.pif', '.com']
    file_name_lower = value.name.lower()
    for ext in dangerous_extensions:
        if ext in file_name_lower:
            raise ValidationError("Този тип файл не е разрешен от съображения за сигурност.")

    # Проверка за валидно име на файл
    if len(value.name) > 255:
        raise ValidationError("Името на файла е твърде дълго.")

    # Проверка за валидни символи в името
    import re
    if not re.match(r'^[a-zA-Z0-9._\-\s\u0400-\u04FF]+$', value.name):
        raise ValidationError("Името на файла съдържа невалидни символи.")

class UploadedDocumentSerializer(serializers.ModelSerializer):
    """
    Сериализатор за UploadedDocument. Използва се за създаване и визуализация на качени документи.
    """
    file = serializers.FileField(validators=[validate_file_extension, validate_file_size, validate_file_content])
    class Meta:
        model = UploadedDocument
        fields = ['id', 'title', 'file', 'text_content', 'status', 'created_at', 'updated_at']
        read_only_fields = ['id', 'text_content', 'status', 'created_at', 'updated_at']

@extend_schema_serializer(
    examples=[
        OpenApiExample(
            'Въпрос (пример)',
            value={
                "id": 1,
                "document": "1cd4f960-a201-4f92-a5a9-eeb9d0de2b15",
                "question_text": "What is Newton's Second Law?",
                "choices": ["F=ma", "E=mc^2", "a^2 + b^2 = c^2", "pV = nRT"],
                "correct_choice": "F=ma",
                "explanation": "It defines force as mass times acceleration.",
                "created_at": "2025-06-02T12:01:05Z",
                "updated_at": "2025-06-02T12:01:05Z"
            },
            response_only=True
        )
    ]
)
class QuestionSerializer(serializers.ModelSerializer):
    """
    Сериализатор за AI въпроси с избираем отговор.
    """
    class Meta:
        model = Question
        fields = ['id', 'document', 'question_text', 'choices', 'correct_choice', 'explanation', 'created_at', 'updated_at']
        read_only_fields = fields

@extend_schema_serializer(
    examples=[
        OpenApiExample(
            'Флашкарта (пример)',
            value={
                "id": 5,
                "document": "1cd4f960-a201-4f92-a5a9-eeb9d0de2b15",
                "front": "Define inertia",
                "back": "The tendency of an object to resist changes in motion.",
                "created_at": "2025-06-02T12:01:10Z",
                "updated_at": "2025-06-02T12:01:10Z"
            },
            response_only=True
        )
    ]
)
class FlashcardSerializer(serializers.ModelSerializer):
    """
    Сериализатор за AI флашкарти.
    """
    class Meta:
        model = Flashcard
        fields = ['id', 'document', 'front', 'back', 'created_at', 'updated_at']
        read_only_fields = fields

@extend_schema_serializer(
    examples=[
        OpenApiExample(
            'Обяснение (пример)',
            value={
                "id": 3,
                "document": "1cd4f960-a201-4f92-a5a9-eeb9d0de2b15",
                "term": "Momentum",
                "definition": "Mass times velocity. A measure of motion.",
                "created_at": "2025-06-02T12:01:12Z",
                "updated_at": "2025-06-02T12:01:12Z"
            },
            response_only=True
        )
    ]
)
class ExplanationSerializer(serializers.ModelSerializer):
    """
    Сериализатор за AI обяснения/термини.
    """
    class Meta:
        model = Explanation
        fields = ['id', 'document', 'term', 'definition', 'created_at', 'updated_at']
        read_only_fields = fields
        