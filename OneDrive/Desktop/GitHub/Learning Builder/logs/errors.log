ERROR 2025-06-28 19:24:26,679 log 14148 3604 Internal Server Error: /api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/generate/flashcards/
ERROR 2025-06-28 19:24:26,680 basehttp 14148 3604 "POST /api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/generate/flashcards/ HTTP/1.1" 500 92
ERROR 2025-06-28 19:24:29,800 log 14148 21056 Internal Server Error: /api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/generate/questions/
ERROR 2025-06-28 19:24:29,801 basehttp 14148 21056 "POST /api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/generate/questions/ HTTP/1.1" 500 74
ERROR 2025-06-28 19:24:47,704 log 14148 20312 Internal Server Error: /api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/generate/flashcards/
ERROR 2025-06-28 19:24:47,710 basehttp 14148 20312 "POST /api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/generate/flashcards/ HTTP/1.1" 500 92
ERROR 2025-06-28 19:24:49,260 log 14148 19040 Internal Server Error: /api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/generate/flashcards/
ERROR 2025-06-28 19:24:49,261 basehttp 14148 19040 "POST /api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/generate/flashcards/ HTTP/1.1" 500 92
ERROR 2025-06-28 20:38:33,047 log 13688 3240 Internal Server Error: /api/courses/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 243, in get
    return self.list(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 40, in get_queryset
    return Course.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x00000295565BB5B0>.
ERROR 2025-06-28 20:38:33,050 log 13688 3056 Internal Server Error: /api/courses/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 243, in get
    return self.list(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 40, in get_queryset
    return Course.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x00000295565BAAA0>.
ERROR 2025-06-28 20:38:33,061 basehttp 13688 3240 "GET /api/courses/ HTTP/1.1" 500 170572
ERROR 2025-06-28 20:38:33,064 basehttp 13688 3056 "GET /api/courses/ HTTP/1.1" 500 170572
ERROR 2025-06-28 20:39:17,158 log 13688 4340 Internal Server Error: /api/courses/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 243, in get
    return self.list(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 40, in get_queryset
    return Course.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000029556433D30>.
ERROR 2025-06-28 20:39:17,181 log 13688 21460 Internal Server Error: /api/courses/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 243, in get
    return self.list(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 40, in get_queryset
    return Course.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x00000295564D9420>.
ERROR 2025-06-28 20:39:17,196 basehttp 13688 4340 "GET /api/courses/ HTTP/1.1" 500 170572
ERROR 2025-06-28 20:39:17,205 basehttp 13688 21460 "GET /api/courses/ HTTP/1.1" 500 170572
ERROR 2025-06-28 20:39:17,219 log 13688 6464 Internal Server Error: /api/documents/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 203, in get
    return self.list(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 66, in get_queryset
    queryset = Document.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000029556701390>.
ERROR 2025-06-28 20:39:17,230 basehttp 13688 6464 "GET /api/documents/ HTTP/1.1" 500 170527
ERROR 2025-06-28 20:46:58,017 log 3360 18012 Internal Server Error: /api/courses/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 243, in get
    return self.list(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 40, in get_queryset
    return Course.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001CAA4E285E0>.
ERROR 2025-06-28 20:46:58,034 log 3360 4820 Internal Server Error: /api/courses/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 243, in get
    return self.list(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 40, in get_queryset
    return Course.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001CAA4E65A50>.
ERROR 2025-06-28 20:46:58,068 log 3360 14152 Internal Server Error: /api/courses/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 243, in get
    return self.list(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 40, in get_queryset
    return Course.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001CAA4E646D0>.
ERROR 2025-06-28 20:46:58,072 log 3360 10548 Internal Server Error: /api/documents/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 203, in get
    return self.list(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 66, in get_queryset
    queryset = Document.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001CAA4E65000>.
ERROR 2025-06-28 20:46:58,076 log 3360 7528 Internal Server Error: /api/documents/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 203, in get
    return self.list(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 66, in get_queryset
    queryset = Document.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001CAA4E66380>.
ERROR 2025-06-28 20:46:58,094 log 3360 17432 Internal Server Error: /api/courses/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 243, in get
    return self.list(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 40, in get_queryset
    return Course.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001CAA4E2BD00>.
ERROR 2025-06-28 20:46:58,119 basehttp 3360 18012 "GET /api/courses/ HTTP/1.1" 500 171328
ERROR 2025-06-28 20:46:58,119 basehttp 3360 4820 "GET /api/courses/ HTTP/1.1" 500 171328
ERROR 2025-06-28 20:46:58,125 basehttp 3360 14152 "GET /api/courses/ HTTP/1.1" 500 171328
ERROR 2025-06-28 20:46:58,128 basehttp 3360 10548 "GET /api/documents/ HTTP/1.1" 500 171283
ERROR 2025-06-28 20:46:58,129 basehttp 3360 7528 "GET /api/documents/ HTTP/1.1" 500 171283
ERROR 2025-06-28 20:46:58,132 basehttp 3360 17432 "GET /api/courses/ HTTP/1.1" 500 171328
