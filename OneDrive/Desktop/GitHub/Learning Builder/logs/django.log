INFO 2025-06-28 19:20:47,032 autoreload 16952 18216 Watching for file changes with StatReloader
INFO 2025-06-28 19:21:42,366 autoreload 9996 7952 Watching for file changes with StatReloader
INFO 2025-06-28 19:23:00,739 autoreload 9996 7952 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\Lib\site-packages\reportlab\lib\rl_accel.py changed, reloading.
INFO 2025-06-28 19:23:01,999 autoreload 21336 20260 Watching for file changes with StatReloader
INFO 2025-06-28 19:23:04,787 autoreload 21336 20260 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\Lib\site-packages\dotenv\__init__.py changed, reloading.
INFO 2025-06-28 19:23:06,072 autoreload 10304 7516 Watching for file changes with StatReloader
INFO 2025-06-28 19:23:12,240 autoreload 10304 7516 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\Lib\site-packages\drf_spectacular\generators.py changed, reloading.
INFO 2025-06-28 19:23:13,348 autoreload 10200 10896 Watching for file changes with StatReloader
INFO 2025-06-28 19:23:24,320 autoreload 14148 14304 Watching for file changes with StatReloader
INFO 2025-06-28 19:24:23,261 middleware 14148 15136 {"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "2047", "upload_size": 1806}
INFO 2025-06-28 19:24:23,355 middleware 14148 15136 {"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 201, "duration_ms": 94.26, "response_size": 62, "ip_address": "127.0.0.1"}
INFO 2025-06-28 19:24:23,357 basehttp 14148 15136 "POST /api/documents/upload/ HTTP/1.1" 201 62
INFO 2025-06-28 19:24:23,376 middleware 14148 21164 {"event": "request_start", "method": "GET", "path": "/api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/flashcards/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
INFO 2025-06-28 19:24:23,395 middleware 14148 7112 {"event": "request_start", "method": "GET", "path": "/api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/flashcards/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
INFO 2025-06-28 19:24:23,426 middleware 14148 21164 {"event": "request_complete", "method": "GET", "path": "/api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/flashcards/", "status_code": 200, "duration_ms": 50.32, "response_size": 2, "ip_address": "127.0.0.1"}
INFO 2025-06-28 19:24:23,427 basehttp 14148 21164 "GET /api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/flashcards/ HTTP/1.1" 200 2
INFO 2025-06-28 19:24:23,438 middleware 14148 7112 {"event": "request_complete", "method": "GET", "path": "/api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/flashcards/", "status_code": 200, "duration_ms": 42.83, "response_size": 2, "ip_address": "127.0.0.1"}
INFO 2025-06-28 19:24:23,440 basehttp 14148 7112 "GET /api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/flashcards/ HTTP/1.1" 200 2
INFO 2025-06-28 19:24:26,618 middleware 14148 3604 {"event": "request_start", "method": "POST", "path": "/api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/generate/flashcards/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "0", "upload_size": 0}
ERROR 2025-06-28 19:24:26,678 middleware 14148 3604 {"event": "request_complete", "method": "POST", "path": "/api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/generate/flashcards/", "status_code": 500, "duration_ms": 60.09, "response_size": 92, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 19:24:26,679 log 14148 3604 Internal Server Error: /api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/generate/flashcards/
ERROR 2025-06-28 19:24:26,680 basehttp 14148 3604 "POST /api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/generate/flashcards/ HTTP/1.1" 500 92
INFO 2025-06-28 19:24:28,742 middleware 14148 20584 {"event": "request_start", "method": "GET", "path": "/api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/questions/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
INFO 2025-06-28 19:24:28,762 middleware 14148 18224 {"event": "request_start", "method": "GET", "path": "/api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/questions/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
INFO 2025-06-28 19:24:28,772 middleware 14148 20584 {"event": "request_complete", "method": "GET", "path": "/api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/questions/", "status_code": 200, "duration_ms": 30.08, "response_size": 2, "ip_address": "127.0.0.1"}
INFO 2025-06-28 19:24:28,773 basehttp 14148 20584 "GET /api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/questions/ HTTP/1.1" 200 2
INFO 2025-06-28 19:24:28,811 middleware 14148 18224 {"event": "request_complete", "method": "GET", "path": "/api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/questions/", "status_code": 200, "duration_ms": 49.32, "response_size": 2, "ip_address": "127.0.0.1"}
INFO 2025-06-28 19:24:28,813 basehttp 14148 18224 "GET /api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/questions/ HTTP/1.1" 200 2
INFO 2025-06-28 19:24:29,748 middleware 14148 21056 {"event": "request_start", "method": "POST", "path": "/api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/generate/questions/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "0", "upload_size": 0}
ERROR 2025-06-28 19:24:29,799 middleware 14148 21056 {"event": "request_complete", "method": "POST", "path": "/api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/generate/questions/", "status_code": 500, "duration_ms": 52.27, "response_size": 74, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 19:24:29,800 log 14148 21056 Internal Server Error: /api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/generate/questions/
ERROR 2025-06-28 19:24:29,801 basehttp 14148 21056 "POST /api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/generate/questions/ HTTP/1.1" 500 74
INFO 2025-06-28 19:24:44,665 middleware 14148 18756 {"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "98457", "upload_size": 98163}
INFO 2025-06-28 19:24:44,721 middleware 14148 18756 {"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 201, "duration_ms": 57.42, "response_size": 62, "ip_address": "127.0.0.1"}
INFO 2025-06-28 19:24:44,722 basehttp 14148 18756 "POST /api/documents/upload/ HTTP/1.1" 201 62
INFO 2025-06-28 19:24:44,737 middleware 14148 7520 {"event": "request_start", "method": "GET", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/flashcards/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
INFO 2025-06-28 19:24:44,775 middleware 14148 7520 {"event": "request_complete", "method": "GET", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/flashcards/", "status_code": 200, "duration_ms": 37.99, "response_size": 2, "ip_address": "127.0.0.1"}
INFO 2025-06-28 19:24:44,777 basehttp 14148 7520 "GET /api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/flashcards/ HTTP/1.1" 200 2
INFO 2025-06-28 19:24:47,554 middleware 14148 20312 {"event": "request_start", "method": "POST", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/generate/flashcards/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "0", "upload_size": 0}
ERROR 2025-06-28 19:24:47,704 middleware 14148 20312 {"event": "request_complete", "method": "POST", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/generate/flashcards/", "status_code": 500, "duration_ms": 150.63, "response_size": 92, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 19:24:47,704 log 14148 20312 Internal Server Error: /api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/generate/flashcards/
ERROR 2025-06-28 19:24:47,710 basehttp 14148 20312 "POST /api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/generate/flashcards/ HTTP/1.1" 500 92
INFO 2025-06-28 19:24:49,062 middleware 14148 19040 {"event": "request_start", "method": "POST", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/generate/flashcards/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "0", "upload_size": 0}
ERROR 2025-06-28 19:24:49,259 middleware 14148 19040 {"event": "request_complete", "method": "POST", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/generate/flashcards/", "status_code": 500, "duration_ms": 197.0, "response_size": 92, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 19:24:49,260 log 14148 19040 Internal Server Error: /api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/generate/flashcards/
ERROR 2025-06-28 19:24:49,261 basehttp 14148 19040 "POST /api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/generate/flashcards/ HTTP/1.1" 500 92
