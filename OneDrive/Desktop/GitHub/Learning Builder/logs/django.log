INFO 2025-06-28 19:20:47,032 autoreload 16952 18216 Watching for file changes with StatReloader
INFO 2025-06-28 19:21:42,366 autoreload 9996 7952 Watching for file changes with StatReloader
INFO 2025-06-28 19:23:00,739 autoreload 9996 7952 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\Lib\site-packages\reportlab\lib\rl_accel.py changed, reloading.
INFO 2025-06-28 19:23:01,999 autoreload 21336 20260 Watching for file changes with StatReloader
INFO 2025-06-28 19:23:04,787 autoreload 21336 20260 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\Lib\site-packages\dotenv\__init__.py changed, reloading.
INFO 2025-06-28 19:23:06,072 autoreload 10304 7516 Watching for file changes with StatReloader
INFO 2025-06-28 19:23:12,240 autoreload 10304 7516 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\Lib\site-packages\drf_spectacular\generators.py changed, reloading.
INFO 2025-06-28 19:23:13,348 autoreload 10200 10896 Watching for file changes with StatReloader
INFO 2025-06-28 19:23:24,320 autoreload 14148 14304 Watching for file changes with StatReloader
INFO 2025-06-28 19:24:23,261 middleware 14148 15136 {"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "2047", "upload_size": 1806}
INFO 2025-06-28 19:24:23,355 middleware 14148 15136 {"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 201, "duration_ms": 94.26, "response_size": 62, "ip_address": "127.0.0.1"}
INFO 2025-06-28 19:24:23,357 basehttp 14148 15136 "POST /api/documents/upload/ HTTP/1.1" 201 62
INFO 2025-06-28 19:24:23,376 middleware 14148 21164 {"event": "request_start", "method": "GET", "path": "/api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/flashcards/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
INFO 2025-06-28 19:24:23,395 middleware 14148 7112 {"event": "request_start", "method": "GET", "path": "/api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/flashcards/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
INFO 2025-06-28 19:24:23,426 middleware 14148 21164 {"event": "request_complete", "method": "GET", "path": "/api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/flashcards/", "status_code": 200, "duration_ms": 50.32, "response_size": 2, "ip_address": "127.0.0.1"}
INFO 2025-06-28 19:24:23,427 basehttp 14148 21164 "GET /api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/flashcards/ HTTP/1.1" 200 2
INFO 2025-06-28 19:24:23,438 middleware 14148 7112 {"event": "request_complete", "method": "GET", "path": "/api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/flashcards/", "status_code": 200, "duration_ms": 42.83, "response_size": 2, "ip_address": "127.0.0.1"}
INFO 2025-06-28 19:24:23,440 basehttp 14148 7112 "GET /api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/flashcards/ HTTP/1.1" 200 2
INFO 2025-06-28 19:24:26,618 middleware 14148 3604 {"event": "request_start", "method": "POST", "path": "/api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/generate/flashcards/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "0", "upload_size": 0}
ERROR 2025-06-28 19:24:26,678 middleware 14148 3604 {"event": "request_complete", "method": "POST", "path": "/api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/generate/flashcards/", "status_code": 500, "duration_ms": 60.09, "response_size": 92, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 19:24:26,679 log 14148 3604 Internal Server Error: /api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/generate/flashcards/
ERROR 2025-06-28 19:24:26,680 basehttp 14148 3604 "POST /api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/generate/flashcards/ HTTP/1.1" 500 92
INFO 2025-06-28 19:24:28,742 middleware 14148 20584 {"event": "request_start", "method": "GET", "path": "/api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/questions/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
INFO 2025-06-28 19:24:28,762 middleware 14148 18224 {"event": "request_start", "method": "GET", "path": "/api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/questions/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
INFO 2025-06-28 19:24:28,772 middleware 14148 20584 {"event": "request_complete", "method": "GET", "path": "/api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/questions/", "status_code": 200, "duration_ms": 30.08, "response_size": 2, "ip_address": "127.0.0.1"}
INFO 2025-06-28 19:24:28,773 basehttp 14148 20584 "GET /api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/questions/ HTTP/1.1" 200 2
INFO 2025-06-28 19:24:28,811 middleware 14148 18224 {"event": "request_complete", "method": "GET", "path": "/api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/questions/", "status_code": 200, "duration_ms": 49.32, "response_size": 2, "ip_address": "127.0.0.1"}
INFO 2025-06-28 19:24:28,813 basehttp 14148 18224 "GET /api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/questions/ HTTP/1.1" 200 2
INFO 2025-06-28 19:24:29,748 middleware 14148 21056 {"event": "request_start", "method": "POST", "path": "/api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/generate/questions/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "0", "upload_size": 0}
ERROR 2025-06-28 19:24:29,799 middleware 14148 21056 {"event": "request_complete", "method": "POST", "path": "/api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/generate/questions/", "status_code": 500, "duration_ms": 52.27, "response_size": 74, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 19:24:29,800 log 14148 21056 Internal Server Error: /api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/generate/questions/
ERROR 2025-06-28 19:24:29,801 basehttp 14148 21056 "POST /api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/generate/questions/ HTTP/1.1" 500 74
INFO 2025-06-28 19:24:44,665 middleware 14148 18756 {"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "98457", "upload_size": 98163}
INFO 2025-06-28 19:24:44,721 middleware 14148 18756 {"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 201, "duration_ms": 57.42, "response_size": 62, "ip_address": "127.0.0.1"}
INFO 2025-06-28 19:24:44,722 basehttp 14148 18756 "POST /api/documents/upload/ HTTP/1.1" 201 62
INFO 2025-06-28 19:24:44,737 middleware 14148 7520 {"event": "request_start", "method": "GET", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/flashcards/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
INFO 2025-06-28 19:24:44,775 middleware 14148 7520 {"event": "request_complete", "method": "GET", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/flashcards/", "status_code": 200, "duration_ms": 37.99, "response_size": 2, "ip_address": "127.0.0.1"}
INFO 2025-06-28 19:24:44,777 basehttp 14148 7520 "GET /api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/flashcards/ HTTP/1.1" 200 2
INFO 2025-06-28 19:24:47,554 middleware 14148 20312 {"event": "request_start", "method": "POST", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/generate/flashcards/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "0", "upload_size": 0}
ERROR 2025-06-28 19:24:47,704 middleware 14148 20312 {"event": "request_complete", "method": "POST", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/generate/flashcards/", "status_code": 500, "duration_ms": 150.63, "response_size": 92, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 19:24:47,704 log 14148 20312 Internal Server Error: /api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/generate/flashcards/
ERROR 2025-06-28 19:24:47,710 basehttp 14148 20312 "POST /api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/generate/flashcards/ HTTP/1.1" 500 92
INFO 2025-06-28 19:24:49,062 middleware 14148 19040 {"event": "request_start", "method": "POST", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/generate/flashcards/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "0", "upload_size": 0}
ERROR 2025-06-28 19:24:49,259 middleware 14148 19040 {"event": "request_complete", "method": "POST", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/generate/flashcards/", "status_code": 500, "duration_ms": 197.0, "response_size": 92, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 19:24:49,260 log 14148 19040 Internal Server Error: /api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/generate/flashcards/
ERROR 2025-06-28 19:24:49,261 basehttp 14148 19040 "POST /api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/generate/flashcards/ HTTP/1.1" 500 92
INFO 2025-06-28 19:26:27,262 autoreload 14148 14304 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\tasks.py changed, reloading.
INFO 2025-06-28 19:26:28,443 autoreload 3496 16744 Watching for file changes with StatReloader
INFO 2025-06-28 19:26:48,249 autoreload 3496 16744 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py changed, reloading.
INFO 2025-06-28 19:26:49,510 autoreload 11840 8744 Watching for file changes with StatReloader
INFO 2025-06-28 19:28:20,883 autoreload 11840 8744 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\ai\client.py changed, reloading.
INFO 2025-06-28 19:28:22,759 autoreload 15760 8416 Watching for file changes with StatReloader
INFO 2025-06-28 19:28:31,087 autoreload 15760 8416 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\Learning_Builder\settings.py changed, reloading.
INFO 2025-06-28 19:28:32,796 autoreload 20712 9544 Watching for file changes with StatReloader
INFO 2025-06-28 19:28:47,820 autoreload 20712 9544 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\Lib\site-packages\openai\types\audio\transcription_create_params.py changed, reloading.
INFO 2025-06-28 19:28:49,730 autoreload 10800 5836 Watching for file changes with StatReloader
INFO 2025-06-28 19:29:12,526 autoreload 10800 5836 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\Lib\site-packages\httpx\__version__.py changed, reloading.
INFO 2025-06-28 19:29:15,000 autoreload 15444 4500 Watching for file changes with StatReloader
INFO 2025-06-28 19:30:04,229 middleware 15444 7484 {"event": "request_start", "method": "POST", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/generate/flashcards/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "0", "upload_size": 0}
INFO 2025-06-28 19:30:04,415 cache 15444 7484 Cache miss for flashcards with key ai_result:flashcards:bc67034f5423dadf
INFO 2025-06-28 19:30:09,554 autoreload 8976 3204 Watching for file changes with StatReloader
INFO 2025-06-28 19:30:10,205 cache 15444 7484 Cached flashcards result with key ai_result:flashcards:bc67034f5423dadf for 604800 seconds
INFO 2025-06-28 19:30:10,464 middleware 15444 7484 {"event": "request_complete", "method": "POST", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/generate/flashcards/", "status_code": 200, "duration_ms": 6234.64, "response_size": 15, "ip_address": "127.0.0.1"}
INFO 2025-06-28 19:30:10,465 basehttp 15444 7484 "POST /api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/generate/flashcards/ HTTP/1.1" 200 15
INFO 2025-06-28 19:30:10,480 middleware 15444 20436 {"event": "request_start", "method": "GET", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/flashcards/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
INFO 2025-06-28 19:30:10,522 middleware 15444 20436 {"event": "request_complete", "method": "GET", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/flashcards/", "status_code": 200, "duration_ms": 42.12, "response_size": 5124, "ip_address": "127.0.0.1"}
INFO 2025-06-28 19:30:10,527 basehttp 15444 20436 "GET /api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/flashcards/ HTTP/1.1" 200 5124
INFO 2025-06-28 19:30:31,351 middleware 15444 16992 {"event": "request_start", "method": "GET", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/questions/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
INFO 2025-06-28 19:30:31,352 middleware 15444 3620 {"event": "request_start", "method": "GET", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/questions/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
INFO 2025-06-28 19:30:31,382 middleware 15444 16992 {"event": "request_complete", "method": "GET", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/questions/", "status_code": 200, "duration_ms": 30.21, "response_size": 2, "ip_address": "127.0.0.1"}
INFO 2025-06-28 19:30:31,383 basehttp 15444 16992 "GET /api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/questions/ HTTP/1.1" 200 2
INFO 2025-06-28 19:30:31,397 middleware 15444 3620 {"event": "request_complete", "method": "GET", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/questions/", "status_code": 200, "duration_ms": 45.01, "response_size": 2, "ip_address": "127.0.0.1"}
INFO 2025-06-28 19:30:31,398 basehttp 15444 3620 "GET /api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/questions/ HTTP/1.1" 200 2
INFO 2025-06-28 19:30:32,411 middleware 15444 19268 {"event": "request_start", "method": "POST", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/generate/questions/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "0", "upload_size": 0}
INFO 2025-06-28 19:30:32,601 cache 15444 19268 Cache miss for questions with key ai_result:questions:abb5dfd3fb19bd4c
INFO 2025-06-28 19:30:48,883 cache 15444 19268 Cached questions result with key ai_result:questions:abb5dfd3fb19bd4c for 604800 seconds
INFO 2025-06-28 19:30:48,989 middleware 15444 19268 {"event": "request_complete", "method": "POST", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/generate/questions/", "status_code": 200, "duration_ms": 16577.25, "response_size": 15, "ip_address": "127.0.0.1"}
INFO 2025-06-28 19:30:48,990 basehttp 15444 19268 "POST /api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/generate/questions/ HTTP/1.1" 200 15
INFO 2025-06-28 19:30:49,003 middleware 15444 5808 {"event": "request_start", "method": "GET", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/questions/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
INFO 2025-06-28 19:30:49,032 middleware 15444 5808 {"event": "request_complete", "method": "GET", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/questions/", "status_code": 200, "duration_ms": 29.33, "response_size": 10638, "ip_address": "127.0.0.1"}
INFO 2025-06-28 19:30:49,034 basehttp 15444 5808 "GET /api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/questions/ HTTP/1.1" 200 10638
INFO 2025-06-28 19:33:22,466 middleware 15444 7140 {"event": "request_start", "method": "GET", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/export/pdf/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
INFO 2025-06-28 19:33:22,576 middleware 15444 7140 {"event": "request_complete", "method": "GET", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/export/pdf/", "status_code": 200, "duration_ms": 111.24, "response_size": 0, "ip_address": "127.0.0.1"}
INFO 2025-06-28 19:33:22,577 basehttp 15444 7140 "GET /api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/export/pdf/ HTTP/1.1" 200 4271
INFO 2025-06-28 19:33:27,227 middleware 15444 4972 {"event": "request_start", "method": "GET", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/export/csv/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
INFO 2025-06-28 19:33:27,263 middleware 15444 4972 {"event": "request_complete", "method": "GET", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/export/csv/", "status_code": 200, "duration_ms": 35.83, "response_size": 0, "ip_address": "127.0.0.1"}
INFO 2025-06-28 19:33:27,266 basehttp 15444 4972 "GET /api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/export/csv/ HTTP/1.1" 200 6347
INFO 2025-06-28 19:33:52,320 middleware 15444 19364 {"event": "request_start", "method": "GET", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/export/anki/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
INFO 2025-06-28 19:33:52,455 middleware 15444 19364 {"event": "request_complete", "method": "GET", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/export/anki/", "status_code": 200, "duration_ms": 134.81, "response_size": 0, "ip_address": "127.0.0.1"}
INFO 2025-06-28 19:33:52,456 basehttp 15444 19364 "GET /api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/export/anki/ HTTP/1.1" 200 61658
INFO 2025-06-28 20:08:18,192 autoreload 8976 3204 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\models.py changed, reloading.
INFO 2025-06-28 20:08:18,657 autoreload 15444 4500 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\models.py changed, reloading.
INFO 2025-06-28 20:08:19,615 autoreload 14752 20296 Watching for file changes with StatReloader
INFO 2025-06-28 20:08:20,101 autoreload 20848 14108 Watching for file changes with StatReloader
INFO 2025-06-28 20:08:56,095 autoreload 14752 20296 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\models.py changed, reloading.
INFO 2025-06-28 20:08:56,153 autoreload 20848 14108 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\models.py changed, reloading.
INFO 2025-06-28 20:08:58,096 autoreload 19396 17192 Watching for file changes with StatReloader
INFO 2025-06-28 20:08:58,156 autoreload 12508 16996 Watching for file changes with StatReloader
INFO 2025-06-28 20:09:24,761 autoreload 12508 16996 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\serializers.py changed, reloading.
INFO 2025-06-28 20:09:24,765 autoreload 19396 17192 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\serializers.py changed, reloading.
INFO 2025-06-28 20:09:25,907 autoreload 6108 9724 Watching for file changes with StatReloader
INFO 2025-06-28 20:09:25,908 autoreload 10884 15820 Watching for file changes with StatReloader
INFO 2025-06-28 20:13:00,252 autoreload 6108 9724 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\models.py changed, reloading.
INFO 2025-06-28 20:13:00,280 autoreload 10884 15820 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\models.py changed, reloading.
INFO 2025-06-28 20:13:01,696 autoreload 13092 21260 Watching for file changes with StatReloader
INFO 2025-06-28 20:13:01,697 autoreload 8784 13236 Watching for file changes with StatReloader
INFO 2025-06-28 20:13:12,020 autoreload 13092 21260 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\models.py changed, reloading.
INFO 2025-06-28 20:13:12,052 autoreload 8784 13236 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\models.py changed, reloading.
INFO 2025-06-28 20:13:13,643 autoreload 5536 19752 Watching for file changes with StatReloader
INFO 2025-06-28 20:13:13,645 autoreload 14680 5760 Watching for file changes with StatReloader
INFO 2025-06-28 20:14:37,386 autoreload 5536 19752 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\Learning_Builder\settings.py changed, reloading.
INFO 2025-06-28 20:14:37,399 autoreload 14680 5760 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\Learning_Builder\settings.py changed, reloading.
INFO 2025-06-28 20:14:39,282 autoreload 9768 19532 Watching for file changes with StatReloader
INFO 2025-06-28 20:14:39,292 autoreload 20156 10832 Watching for file changes with StatReloader
INFO 2025-06-28 20:14:55,343 autoreload 20156 10832 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\models.py changed, reloading.
INFO 2025-06-28 20:14:55,355 autoreload 9768 19532 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\models.py changed, reloading.
INFO 2025-06-28 20:14:56,991 autoreload 14812 16652 Watching for file changes with StatReloader
INFO 2025-06-28 20:14:56,995 autoreload 16888 13556 Watching for file changes with StatReloader
INFO 2025-06-28 20:15:04,413 autoreload 14812 16652 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\models.py changed, reloading.
INFO 2025-06-28 20:15:04,464 autoreload 16888 13556 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\models.py changed, reloading.
INFO 2025-06-28 20:15:06,563 autoreload 10548 21060 Watching for file changes with StatReloader
INFO 2025-06-28 20:15:06,653 autoreload 12380 20384 Watching for file changes with StatReloader
INFO 2025-06-28 20:16:19,489 autoreload 12380 20384 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\models.py changed, reloading.
INFO 2025-06-28 20:16:19,530 autoreload 10548 21060 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\models.py changed, reloading.
INFO 2025-06-28 20:16:20,712 autoreload 8968 15180 Watching for file changes with StatReloader
INFO 2025-06-28 20:16:20,717 autoreload 5252 3396 Watching for file changes with StatReloader
INFO 2025-06-28 20:20:36,140 autoreload 5252 3396 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\Learning_Builder\settings.py changed, reloading.
INFO 2025-06-28 20:20:36,141 autoreload 8968 15180 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\Learning_Builder\settings.py changed, reloading.
INFO 2025-06-28 20:20:37,488 autoreload 17020 4860 Watching for file changes with StatReloader
INFO 2025-06-28 20:20:37,490 autoreload 5128 14656 Watching for file changes with StatReloader
INFO 2025-06-28 20:24:42,496 autoreload 17020 4860 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\Learning_Builder\urls.py changed, reloading.
INFO 2025-06-28 20:24:42,786 autoreload 5128 14656 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\Learning_Builder\urls.py changed, reloading.
INFO 2025-06-28 20:24:44,317 autoreload 13260 19380 Watching for file changes with StatReloader
INFO 2025-06-28 20:24:44,506 autoreload 12380 19836 Watching for file changes with StatReloader
INFO 2025-06-28 20:25:04,193 autoreload 13260 19380 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\Learning_Builder\urls.py changed, reloading.
INFO 2025-06-28 20:25:04,313 autoreload 12380 19836 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\Learning_Builder\urls.py changed, reloading.
INFO 2025-06-28 20:25:05,848 autoreload 13688 20900 Watching for file changes with StatReloader
INFO 2025-06-28 20:25:05,990 autoreload 13548 15832 Watching for file changes with StatReloader
INFO 2025-06-28 20:25:30,538 autoreload 10144 13324 Watching for file changes with StatReloader
INFO 2025-06-28 20:25:55,317 middleware 13688 16328 {"event": "request_start", "method": "GET", "path": "/api/docs/", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; bg-BG) WindowsPowerShell/5.1.26100.4484", "ip_address": "127.0.0.1", "content_length": ""}
INFO 2025-06-28 20:25:55,333 middleware 13688 16328 {"event": "request_complete", "method": "GET", "path": "/api/docs/", "status_code": 200, "duration_ms": 15.99, "response_size": 4640, "ip_address": "127.0.0.1"}
INFO 2025-06-28 20:25:55,333 basehttp 13688 16328 "GET /api/docs/ HTTP/1.1" 200 4640
INFO 2025-06-28 20:25:56,070 basehttp 13688 16328 - Broken pipe from ('127.0.0.1', 60610)
INFO 2025-06-28 20:31:28,190 autoreload 11120 21468 Watching for file changes with StatReloader
INFO 2025-06-28 20:31:46,969 middleware 13688 15924 {"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "98457", "upload_size": 98163}
WARNING 2025-06-28 20:31:46,998 middleware 13688 15924 {"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 400, "duration_ms": 31.04, "response_size": 37, "ip_address": "127.0.0.1"}
WARNING 2025-06-28 20:31:46,998 log 13688 15924 Bad Request: /api/documents/upload/
WARNING 2025-06-28 20:31:46,999 basehttp 13688 15924 "POST /api/documents/upload/ HTTP/1.1" 400 37
INFO 2025-06-28 20:32:19,958 middleware 13688 17416 {"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "98457", "upload_size": 98163}
WARNING 2025-06-28 20:32:19,964 middleware 13688 17416 {"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 400, "duration_ms": 5.69, "response_size": 37, "ip_address": "127.0.0.1"}
WARNING 2025-06-28 20:32:19,964 log 13688 17416 Bad Request: /api/documents/upload/
WARNING 2025-06-28 20:32:19,968 basehttp 13688 17416 "POST /api/documents/upload/ HTTP/1.1" 400 37
INFO 2025-06-28 20:32:29,652 middleware 13688 15796 {"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "2051", "upload_size": 1806}
WARNING 2025-06-28 20:32:29,657 middleware 13688 15796 {"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 400, "duration_ms": 6.53, "response_size": 37, "ip_address": "127.0.0.1"}
WARNING 2025-06-28 20:32:29,660 log 13688 15796 Bad Request: /api/documents/upload/
WARNING 2025-06-28 20:32:29,661 basehttp 13688 15796 "POST /api/documents/upload/ HTTP/1.1" 400 37
INFO 2025-06-28 20:32:31,611 middleware 13688 20488 {"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "2051", "upload_size": 1806}
WARNING 2025-06-28 20:32:31,618 middleware 13688 20488 {"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 400, "duration_ms": 7.0, "response_size": 37, "ip_address": "127.0.0.1"}
WARNING 2025-06-28 20:32:31,619 log 13688 20488 Bad Request: /api/documents/upload/
WARNING 2025-06-28 20:32:31,619 basehttp 13688 20488 "POST /api/documents/upload/ HTTP/1.1" 400 37
INFO 2025-06-28 20:38:32,827 middleware 13688 3056 {"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
ERROR 2025-06-28 20:38:32,828 middleware 13688 3056 {"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x00000295565BAAA0>.", "duration_ms": 1.0, "ip_address": "127.0.0.1"}
INFO 2025-06-28 20:38:32,851 middleware 13688 3240 {"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
ERROR 2025-06-28 20:38:32,853 middleware 13688 3240 {"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x00000295565BB5B0>.", "duration_ms": 2.0, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 20:38:33,047 log 13688 3240 Internal Server Error: /api/courses/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 243, in get
    return self.list(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 40, in get_queryset
    return Course.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x00000295565BB5B0>.
ERROR 2025-06-28 20:38:33,050 log 13688 3056 Internal Server Error: /api/courses/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 243, in get
    return self.list(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 40, in get_queryset
    return Course.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x00000295565BAAA0>.
ERROR 2025-06-28 20:38:33,059 middleware 13688 3240 {"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 206.94, "response_size": 170572, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 20:38:33,061 basehttp 13688 3240 "GET /api/courses/ HTTP/1.1" 500 170572
ERROR 2025-06-28 20:38:33,062 middleware 13688 3056 {"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 233.94, "response_size": 170572, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 20:38:33,064 basehttp 13688 3056 "GET /api/courses/ HTTP/1.1" 500 170572
INFO 2025-06-28 20:39:17,035 middleware 13688 21460 {"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
ERROR 2025-06-28 20:39:17,037 middleware 13688 21460 {"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x00000295564D9420>.", "duration_ms": 2.0, "ip_address": "127.0.0.1"}
INFO 2025-06-28 20:39:17,037 middleware 13688 4340 {"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
INFO 2025-06-28 20:39:17,039 middleware 13688 6464 {"event": "request_start", "method": "GET", "path": "/api/documents/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
ERROR 2025-06-28 20:39:17,047 middleware 13688 4340 {"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000029556433D30>.", "duration_ms": 9.53, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 20:39:17,049 middleware 13688 6464 {"event": "request_exception", "method": "GET", "path": "/api/documents/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000029556701390>.", "duration_ms": 10.03, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 20:39:17,158 log 13688 4340 Internal Server Error: /api/courses/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 243, in get
    return self.list(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 40, in get_queryset
    return Course.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000029556433D30>.
ERROR 2025-06-28 20:39:17,181 log 13688 21460 Internal Server Error: /api/courses/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 243, in get
    return self.list(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 40, in get_queryset
    return Course.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x00000295564D9420>.
ERROR 2025-06-28 20:39:17,187 middleware 13688 4340 {"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 149.84, "response_size": 170572, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 20:39:17,191 middleware 13688 21460 {"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 155.88, "response_size": 170572, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 20:39:17,196 basehttp 13688 4340 "GET /api/courses/ HTTP/1.1" 500 170572
ERROR 2025-06-28 20:39:17,205 basehttp 13688 21460 "GET /api/courses/ HTTP/1.1" 500 170572
ERROR 2025-06-28 20:39:17,219 log 13688 6464 Internal Server Error: /api/documents/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 203, in get
    return self.list(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 66, in get_queryset
    queryset = Document.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000029556701390>.
ERROR 2025-06-28 20:39:17,227 middleware 13688 6464 {"event": "request_complete", "method": "GET", "path": "/api/documents/", "status_code": 500, "duration_ms": 187.94, "response_size": 170527, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 20:39:17,230 basehttp 13688 6464 "GET /api/documents/ HTTP/1.1" 500 170527
INFO 2025-06-28 20:42:24,144 middleware 13688 20808 {"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "98457", "upload_size": 98163}
WARNING 2025-06-28 20:42:24,152 middleware 13688 20808 {"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 400, "duration_ms": 8.55, "response_size": 37, "ip_address": "127.0.0.1"}
WARNING 2025-06-28 20:42:24,153 log 13688 20808 Bad Request: /api/documents/upload/
WARNING 2025-06-28 20:42:24,154 basehttp 13688 20808 "POST /api/documents/upload/ HTTP/1.1" 400 37
INFO 2025-06-28 20:45:27,927 autoreload 11120 21468 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\Learning_Builder\settings.py changed, reloading.
INFO 2025-06-28 20:45:27,949 autoreload 10144 13324 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\Learning_Builder\settings.py changed, reloading.
INFO 2025-06-28 20:45:27,998 autoreload 13688 20900 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\Learning_Builder\settings.py changed, reloading.
INFO 2025-06-28 20:45:30,079 autoreload 15180 1608 Watching for file changes with StatReloader
INFO 2025-06-28 20:45:30,083 autoreload 3360 19020 Watching for file changes with StatReloader
INFO 2025-06-28 20:45:30,115 autoreload 21380 15636 Watching for file changes with StatReloader
INFO 2025-06-28 20:45:47,961 autoreload 21376 19984 Watching for file changes with StatReloader
INFO 2025-06-28 20:46:57,720 middleware 3360 18012 {"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
INFO 2025-06-28 20:46:57,721 middleware 3360 17432 {"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
INFO 2025-06-28 20:46:57,721 middleware 3360 10548 {"event": "request_start", "method": "GET", "path": "/api/documents/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
INFO 2025-06-28 20:46:57,721 middleware 3360 14152 {"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
INFO 2025-06-28 20:46:57,722 middleware 3360 4820 {"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
INFO 2025-06-28 20:46:57,724 middleware 3360 7528 {"event": "request_start", "method": "GET", "path": "/api/documents/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
ERROR 2025-06-28 20:46:57,733 middleware 3360 18012 {"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001CAA4E285E0>.", "duration_ms": 12.99, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 20:46:57,733 middleware 3360 17432 {"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001CAA4E2BD00>.", "duration_ms": 12.0, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 20:46:57,734 middleware 3360 14152 {"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001CAA4E646D0>.", "duration_ms": 12.99, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 20:46:57,735 middleware 3360 10548 {"event": "request_exception", "method": "GET", "path": "/api/documents/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001CAA4E65000>.", "duration_ms": 14.0, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 20:46:57,735 middleware 3360 4820 {"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001CAA4E65A50>.", "duration_ms": 13.0, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 20:46:57,736 middleware 3360 7528 {"event": "request_exception", "method": "GET", "path": "/api/documents/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001CAA4E66380>.", "duration_ms": 12.0, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 20:46:58,017 log 3360 18012 Internal Server Error: /api/courses/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 243, in get
    return self.list(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 40, in get_queryset
    return Course.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001CAA4E285E0>.
ERROR 2025-06-28 20:46:58,034 log 3360 4820 Internal Server Error: /api/courses/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 243, in get
    return self.list(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 40, in get_queryset
    return Course.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001CAA4E65A50>.
ERROR 2025-06-28 20:46:58,068 log 3360 14152 Internal Server Error: /api/courses/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 243, in get
    return self.list(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 40, in get_queryset
    return Course.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001CAA4E646D0>.
ERROR 2025-06-28 20:46:58,072 log 3360 10548 Internal Server Error: /api/documents/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 203, in get
    return self.list(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 66, in get_queryset
    queryset = Document.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001CAA4E65000>.
ERROR 2025-06-28 20:46:58,076 log 3360 7528 Internal Server Error: /api/documents/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 203, in get
    return self.list(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 66, in get_queryset
    queryset = Document.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001CAA4E66380>.
ERROR 2025-06-28 20:46:58,094 log 3360 17432 Internal Server Error: /api/courses/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 243, in get
    return self.list(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 40, in get_queryset
    return Course.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001CAA4E2BD00>.
ERROR 2025-06-28 20:46:58,105 middleware 3360 18012 {"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 385.13, "response_size": 171328, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 20:46:58,108 middleware 3360 4820 {"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 386.15, "response_size": 171328, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 20:46:58,116 middleware 3360 14152 {"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 394.14, "response_size": 171328, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 20:46:58,119 basehttp 3360 18012 "GET /api/courses/ HTTP/1.1" 500 171328
ERROR 2025-06-28 20:46:58,119 basehttp 3360 4820 "GET /api/courses/ HTTP/1.1" 500 171328
ERROR 2025-06-28 20:46:58,120 middleware 3360 10548 {"event": "request_complete", "method": "GET", "path": "/api/documents/", "status_code": 500, "duration_ms": 399.15, "response_size": 171283, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 20:46:58,123 middleware 3360 7528 {"event": "request_complete", "method": "GET", "path": "/api/documents/", "status_code": 500, "duration_ms": 399.14, "response_size": 171283, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 20:46:58,125 basehttp 3360 14152 "GET /api/courses/ HTTP/1.1" 500 171328
ERROR 2025-06-28 20:46:58,125 middleware 3360 17432 {"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 404.14, "response_size": 171328, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 20:46:58,128 basehttp 3360 10548 "GET /api/documents/ HTTP/1.1" 500 171283
ERROR 2025-06-28 20:46:58,129 basehttp 3360 7528 "GET /api/documents/ HTTP/1.1" 500 171283
ERROR 2025-06-28 20:46:58,132 basehttp 3360 17432 "GET /api/courses/ HTTP/1.1" 500 171328
INFO 2025-06-28 20:47:05,992 middleware 3360 276 {"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "98457", "upload_size": 98163}
WARNING 2025-06-28 20:47:06,018 middleware 3360 276 {"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 400, "duration_ms": 26.02, "response_size": 37, "ip_address": "127.0.0.1"}
WARNING 2025-06-28 20:47:06,020 log 3360 276 Bad Request: /api/documents/upload/
WARNING 2025-06-28 20:47:06,020 basehttp 3360 276 "POST /api/documents/upload/ HTTP/1.1" 400 37
