{"level": "INFO", "time": "2025-06-28 19:24:23,261", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "2047", "upload_size": 1806}"}
{"level": "INFO", "time": "2025-06-28 19:24:23,355", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 201, "duration_ms": 94.26, "response_size": 62, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 19:24:23,376", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/flashcards/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 19:24:23,395", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/flashcards/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 19:24:23,426", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/flashcards/", "status_code": 200, "duration_ms": 50.32, "response_size": 2, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 19:24:23,438", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/flashcards/", "status_code": 200, "duration_ms": 42.83, "response_size": 2, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 19:24:26,618", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/generate/flashcards/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "0", "upload_size": 0}"}
{"level": "ERROR", "time": "2025-06-28 19:24:26,678", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/generate/flashcards/", "status_code": 500, "duration_ms": 60.09, "response_size": 92, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 19:24:28,742", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/questions/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 19:24:28,762", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/questions/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 19:24:28,772", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/questions/", "status_code": 200, "duration_ms": 30.08, "response_size": 2, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 19:24:28,811", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/questions/", "status_code": 200, "duration_ms": 49.32, "response_size": 2, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 19:24:29,748", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/generate/questions/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "0", "upload_size": 0}"}
{"level": "ERROR", "time": "2025-06-28 19:24:29,799", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/generate/questions/", "status_code": 500, "duration_ms": 52.27, "response_size": 74, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 19:24:44,665", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "98457", "upload_size": 98163}"}
{"level": "INFO", "time": "2025-06-28 19:24:44,721", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 201, "duration_ms": 57.42, "response_size": 62, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 19:24:44,737", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/flashcards/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 19:24:44,775", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/flashcards/", "status_code": 200, "duration_ms": 37.99, "response_size": 2, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 19:24:47,554", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/generate/flashcards/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "0", "upload_size": 0}"}
{"level": "ERROR", "time": "2025-06-28 19:24:47,704", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/generate/flashcards/", "status_code": 500, "duration_ms": 150.63, "response_size": 92, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 19:24:49,062", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/generate/flashcards/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "0", "upload_size": 0}"}
{"level": "ERROR", "time": "2025-06-28 19:24:49,259", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/generate/flashcards/", "status_code": 500, "duration_ms": 197.0, "response_size": 92, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 19:27:49,542", "module": "tasks", "message": "Successfully parsed 2 flashcards"}
{"level": "INFO", "time": "2025-06-28 19:27:49,542", "module": "tasks", "message": "Parsing AI flashcards output: {"front": "API", "back": "Application Programming Interface"}..."}
{"level": "WARNING", "time": "2025-06-28 19:27:49,543", "module": "tasks", "message": "All parsing strategies failed, creating fallback flashcards"}
{"level": "INFO", "time": "2025-06-28 19:27:49,544", "module": "tasks", "message": "Successfully parsed 1 flashcards"}
{"level": "INFO", "time": "2025-06-28 19:27:49,544", "module": "tasks", "message": "Parsing AI flashcards output: Random text without proper format..."}
{"level": "WARNING", "time": "2025-06-28 19:27:49,545", "module": "tasks", "message": "All parsing strategies failed, creating fallback flashcards"}
{"level": "INFO", "time": "2025-06-28 19:27:49,545", "module": "tasks", "message": "Successfully parsed 1 flashcards"}
{"level": "INFO", "time": "2025-06-28 19:27:49,546", "module": "tasks", "message": "Parsing AI flashcards output: ..."}
{"level": "WARNING", "time": "2025-06-28 19:27:49,546", "module": "tasks", "message": "All parsing strategies failed, creating fallback flashcards"}
{"level": "INFO", "time": "2025-06-28 19:27:49,547", "module": "tasks", "message": "Successfully parsed 0 flashcards"}
{"level": "INFO", "time": "2025-06-28 19:27:49,550", "module": "tasks", "message": "Successfully parsed 1 questions"}
{"level": "INFO", "time": "2025-06-28 19:27:49,551", "module": "tasks", "message": "Parsing AI questions output: Random question text..."}
{"level": "WARNING", "time": "2025-06-28 19:27:49,552", "module": "tasks", "message": "All parsing strategies failed, creating fallback questions"}
{"level": "INFO", "time": "2025-06-28 19:27:49,552", "module": "tasks", "message": "Successfully parsed 1 questions"}
{"level": "INFO", "time": "2025-06-28 19:27:49,552", "module": "tasks", "message": "Parsing AI questions output: ..."}
{"level": "WARNING", "time": "2025-06-28 19:27:49,552", "module": "tasks", "message": "All parsing strategies failed, creating fallback questions"}
{"level": "INFO", "time": "2025-06-28 19:27:49,553", "module": "tasks", "message": "Successfully parsed 0 questions"}
{"level": "ERROR", "time": "2025-06-28 19:29:00,952", "module": "client", "message": "Failed to initialize OpenAI client: Client.__init__() got an unexpected keyword argument 'proxies'"}
{"level": "ERROR", "time": "2025-06-28 19:29:00,953", "module": "client", "message": "Fallback also failed: Client.__init__() got an unexpected keyword argument 'proxies'"}
{"level": "INFO", "time": "2025-06-28 19:29:00,957", "module": "tasks", "message": "Successfully parsed 2 flashcards"}
{"level": "INFO", "time": "2025-06-28 19:29:00,958", "module": "tasks", "message": "Parsing AI flashcards output: {"front": "API", "back": "Application Programming Interface"}..."}
{"level": "WARNING", "time": "2025-06-28 19:29:00,958", "module": "tasks", "message": "All parsing strategies failed, creating fallback flashcards"}
{"level": "INFO", "time": "2025-06-28 19:29:00,959", "module": "tasks", "message": "Successfully parsed 1 flashcards"}
{"level": "INFO", "time": "2025-06-28 19:29:00,959", "module": "tasks", "message": "Parsing AI flashcards output: Random text without proper format..."}
{"level": "WARNING", "time": "2025-06-28 19:29:00,959", "module": "tasks", "message": "All parsing strategies failed, creating fallback flashcards"}
{"level": "INFO", "time": "2025-06-28 19:29:00,960", "module": "tasks", "message": "Successfully parsed 1 flashcards"}
{"level": "INFO", "time": "2025-06-28 19:29:00,960", "module": "tasks", "message": "Parsing AI flashcards output: ..."}
{"level": "WARNING", "time": "2025-06-28 19:29:00,960", "module": "tasks", "message": "All parsing strategies failed, creating fallback flashcards"}
{"level": "INFO", "time": "2025-06-28 19:29:00,961", "module": "tasks", "message": "Successfully parsed 0 flashcards"}
{"level": "INFO", "time": "2025-06-28 19:29:00,963", "module": "tasks", "message": "Successfully parsed 1 questions"}
{"level": "INFO", "time": "2025-06-28 19:29:00,964", "module": "tasks", "message": "Parsing AI questions output: Random question text..."}
{"level": "WARNING", "time": "2025-06-28 19:29:00,965", "module": "tasks", "message": "All parsing strategies failed, creating fallback questions"}
{"level": "INFO", "time": "2025-06-28 19:29:00,965", "module": "tasks", "message": "Successfully parsed 1 questions"}
{"level": "INFO", "time": "2025-06-28 19:29:00,966", "module": "tasks", "message": "Parsing AI questions output: ..."}
{"level": "WARNING", "time": "2025-06-28 19:29:00,966", "module": "tasks", "message": "All parsing strategies failed, creating fallback questions"}
{"level": "INFO", "time": "2025-06-28 19:29:00,967", "module": "tasks", "message": "Successfully parsed 0 questions"}
{"level": "ERROR", "time": "2025-06-28 19:29:00,967", "module": "client", "message": "Failed to initialize OpenAI client: Client.__init__() got an unexpected keyword argument 'proxies'"}
{"level": "ERROR", "time": "2025-06-28 19:29:00,968", "module": "client", "message": "Fallback also failed: Client.__init__() got an unexpected keyword argument 'proxies'"}
{"level": "ERROR", "time": "2025-06-28 19:29:00,972", "module": "client", "message": "Failed to initialize OpenAI client: Client.__init__() got an unexpected keyword argument 'proxies'"}
{"level": "ERROR", "time": "2025-06-28 19:29:00,973", "module": "client", "message": "Fallback also failed: Client.__init__() got an unexpected keyword argument 'proxies'"}
{"level": "INFO", "time": "2025-06-28 19:29:22,649", "module": "client", "message": "Making OpenAI API call for unknown"}
{"level": "INFO", "time": "2025-06-28 19:29:25,086", "module": "client", "message": "Successfully generated content with 12 characters"}
{"level": "INFO", "time": "2025-06-28 19:29:25,093", "module": "tasks", "message": "Successfully parsed 2 flashcards"}
{"level": "INFO", "time": "2025-06-28 19:29:25,093", "module": "tasks", "message": "Parsing AI flashcards output: {"front": "API", "back": "Application Programming Interface"}..."}
{"level": "WARNING", "time": "2025-06-28 19:29:25,094", "module": "tasks", "message": "All parsing strategies failed, creating fallback flashcards"}
{"level": "INFO", "time": "2025-06-28 19:29:25,094", "module": "tasks", "message": "Successfully parsed 1 flashcards"}
{"level": "INFO", "time": "2025-06-28 19:29:25,095", "module": "tasks", "message": "Parsing AI flashcards output: Random text without proper format..."}
{"level": "WARNING", "time": "2025-06-28 19:29:25,095", "module": "tasks", "message": "All parsing strategies failed, creating fallback flashcards"}
{"level": "INFO", "time": "2025-06-28 19:29:25,096", "module": "tasks", "message": "Successfully parsed 1 flashcards"}
{"level": "INFO", "time": "2025-06-28 19:29:25,096", "module": "tasks", "message": "Parsing AI flashcards output: ..."}
{"level": "WARNING", "time": "2025-06-28 19:29:25,097", "module": "tasks", "message": "All parsing strategies failed, creating fallback flashcards"}
{"level": "INFO", "time": "2025-06-28 19:29:25,097", "module": "tasks", "message": "Successfully parsed 0 flashcards"}
{"level": "INFO", "time": "2025-06-28 19:29:25,100", "module": "tasks", "message": "Successfully parsed 1 questions"}
{"level": "INFO", "time": "2025-06-28 19:29:25,100", "module": "tasks", "message": "Parsing AI questions output: Random question text..."}
{"level": "WARNING", "time": "2025-06-28 19:29:25,101", "module": "tasks", "message": "All parsing strategies failed, creating fallback questions"}
{"level": "INFO", "time": "2025-06-28 19:29:25,103", "module": "tasks", "message": "Successfully parsed 1 questions"}
{"level": "INFO", "time": "2025-06-28 19:29:25,104", "module": "tasks", "message": "Parsing AI questions output: ..."}
{"level": "WARNING", "time": "2025-06-28 19:29:25,105", "module": "tasks", "message": "All parsing strategies failed, creating fallback questions"}
{"level": "INFO", "time": "2025-06-28 19:29:25,105", "module": "tasks", "message": "Successfully parsed 0 questions"}
{"level": "INFO", "time": "2025-06-28 19:29:25,118", "module": "client", "message": "Making OpenAI API call for flashcards"}
{"level": "INFO", "time": "2025-06-28 19:29:30,431", "module": "client", "message": "Successfully generated flashcards with 1407 characters"}
{"level": "INFO", "time": "2025-06-28 19:29:30,434", "module": "tasks", "message": "Successfully parsed 10 flashcards"}
{"level": "INFO", "time": "2025-06-28 19:29:30,447", "module": "client", "message": "Making OpenAI API call for questions"}
{"level": "INFO", "time": "2025-06-28 19:29:45,734", "module": "client", "message": "Successfully generated questions with 3273 characters"}
{"level": "INFO", "time": "2025-06-28 19:29:45,740", "module": "tasks", "message": "Successfully parsed 19 questions"}
{"level": "INFO", "time": "2025-06-28 19:30:04,229", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/generate/flashcards/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "0", "upload_size": 0}"}
{"level": "INFO", "time": "2025-06-28 19:30:04,415", "module": "cache", "message": "Cache miss for flashcards with key ai_result:flashcards:bc67034f5423dadf"}
{"level": "INFO", "time": "2025-06-28 19:30:04,417", "module": "client", "message": "Making OpenAI API call for flashcards"}
{"level": "INFO", "time": "2025-06-28 19:30:10,205", "module": "cache", "message": "Cached flashcards result with key ai_result:flashcards:bc67034f5423dadf for 604800 seconds"}
{"level": "INFO", "time": "2025-06-28 19:30:10,205", "module": "client", "message": "Successfully generated flashcards with 1918 characters"}
{"level": "INFO", "time": "2025-06-28 19:30:10,406", "module": "tasks", "message": "Successfully parsed 10 flashcards"}
{"level": "INFO", "time": "2025-06-28 19:30:10,464", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/generate/flashcards/", "status_code": 200, "duration_ms": 6234.64, "response_size": 15, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 19:30:10,480", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/flashcards/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 19:30:10,522", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/flashcards/", "status_code": 200, "duration_ms": 42.12, "response_size": 5124, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 19:30:31,351", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/questions/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 19:30:31,352", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/questions/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 19:30:31,382", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/questions/", "status_code": 200, "duration_ms": 30.21, "response_size": 2, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 19:30:31,397", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/questions/", "status_code": 200, "duration_ms": 45.01, "response_size": 2, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 19:30:32,411", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/generate/questions/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "0", "upload_size": 0}"}
{"level": "INFO", "time": "2025-06-28 19:30:32,601", "module": "cache", "message": "Cache miss for questions with key ai_result:questions:abb5dfd3fb19bd4c"}
{"level": "INFO", "time": "2025-06-28 19:30:32,601", "module": "client", "message": "Making OpenAI API call for questions"}
{"level": "INFO", "time": "2025-06-28 19:30:48,883", "module": "cache", "message": "Cached questions result with key ai_result:questions:abb5dfd3fb19bd4c for 604800 seconds"}
{"level": "INFO", "time": "2025-06-28 19:30:48,884", "module": "client", "message": "Successfully generated questions with 4448 characters"}
{"level": "INFO", "time": "2025-06-28 19:30:48,902", "module": "tasks", "message": "Successfully parsed 20 questions"}
{"level": "INFO", "time": "2025-06-28 19:30:48,989", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/generate/questions/", "status_code": 200, "duration_ms": 16577.25, "response_size": 15, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 19:30:49,003", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/questions/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 19:30:49,032", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/questions/", "status_code": 200, "duration_ms": 29.33, "response_size": 10638, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 19:33:22,466", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/export/pdf/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 19:33:22,576", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/export/pdf/", "status_code": 200, "duration_ms": 111.24, "response_size": 0, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 19:33:27,227", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/export/csv/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 19:33:27,263", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/export/csv/", "status_code": 200, "duration_ms": 35.83, "response_size": 0, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 19:33:52,320", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/export/anki/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 19:33:52,455", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/export/anki/", "status_code": 200, "duration_ms": 134.81, "response_size": 0, "ip_address": "127.0.0.1"}"}
