"""
URL configuration for Learning_Builder project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
# from django.contrib import admin
from django.urls import path
from documents.views import (
    UserProfileView, CourseListCreateView, CourseDetailView,
    DocumentUploadView, DocumentListView, DocumentDetailView,
    StudySetListView, StudySetDetailView,
    GenerateFlashcardsView, GenerateQuizView,
    FlashcardListView, FlashcardsDueView, QuizDetailView,
    user_statistics
)
from django.conf import settings
from django.conf.urls.static import static
from drf_spectacular.views import SpectacularAPIView, SpectacularSwaggerView

urlpatterns = [
    #    path('admin/', admin.site.urls),

    # User Profile
    path('api/profile/', UserProfileView.as_view(), name='user-profile'),

    # Courses
    path('api/courses/', CourseListCreateView.as_view(), name='course-list-create'),
    path('api/courses/<uuid:pk>/', CourseDetailView.as_view(), name='course-detail'),

    # Documents
    path('api/documents/', DocumentListView.as_view(), name='document-list'),
    path('api/documents/upload/', DocumentUploadView.as_view(), name='document-upload'),
    path('api/documents/<uuid:pk>/', DocumentDetailView.as_view(), name='document-detail'),

    # Study Sets
    path('api/study-sets/', StudySetListView.as_view(), name='study-set-list'),
    path('api/study-sets/<uuid:pk>/', StudySetDetailView.as_view(), name='study-set-detail'),

    # AI Generation
    path('api/documents/<uuid:document_id>/generate/flashcards/', GenerateFlashcardsView.as_view(), name='generate-flashcards'),
    path('api/documents/<uuid:document_id>/generate/quiz/', GenerateQuizView.as_view(), name='generate-quiz'),

    # Flashcards
    path('api/study-sets/<uuid:study_set_id>/flashcards/', FlashcardListView.as_view(), name='flashcard-list'),
    path('api/flashcards/due/', FlashcardsDueView.as_view(), name='flashcards-due'),

    # Quizzes
    path('api/quizzes/<uuid:pk>/', QuizDetailView.as_view(), name='quiz-detail'),

    # Statistics
    path('api/stats/', user_statistics, name='user-statistics'),

    # API Documentation
    path('api/schema/', SpectacularAPIView.as_view(), name='schema'),
    path('api/docs/', SpectacularSwaggerView.as_view(url_name='schema')),
]

# За development: сервирай media файлове
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
