"""
URL configuration for Learning_Builder project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
# from django.contrib import admin
from django.urls import path
# Временно коментирано за миграция
# from documents.views import (
#     DocumentUploadView, DocumentStatusView, DocumentQuestionsView, DocumentFlashcardsView, DocumentExplanationsView,
#     DocumentExportPDFView, DocumentExportAnkiView, DocumentExportCSVView,
#     GenerateQuestionsView, GenerateFlashcardsView, GenerateExplanationsView
# )
from django.conf import settings
from django.conf.urls.static import static
from drf_spectacular.views import SpectacularAPIView, SpectacularSwaggerView

urlpatterns = [
    #    path('admin/', admin.site.urls),
    # Временно коментирано за миграция
    # path('api/documents/upload/', DocumentUploadView.as_view(), name='document-upload'),
    # path('api/documents/status/<uuid:pk>/', DocumentStatusView.as_view(), name='document-status'),
    # path('api/documents/<uuid:document_id>/questions/', DocumentQuestionsView.as_view(), name='document-questions'),
    # path('api/documents/<uuid:document_id>/flashcards/', DocumentFlashcardsView.as_view(), name='document-flashcards'),
    # path('api/documents/<uuid:document_id>/explanations/', DocumentExplanationsView.as_view(), name='document-explanations'),
    # path('api/documents/<uuid:document_id>/export/pdf/', DocumentExportPDFView.as_view(), name='document-export-pdf'),
    # path('api/documents/<uuid:document_id>/export/anki/', DocumentExportAnkiView.as_view(), name='document-export-anki'),
    # path('api/documents/<uuid:document_id>/export/csv/', DocumentExportCSVView.as_view(), name='document-export-csv'),
    # path('api/documents/<uuid:document_id>/generate/questions/', GenerateQuestionsView.as_view(), name='generate-questions'),
    # path('api/documents/<uuid:document_id>/generate/flashcards/', GenerateFlashcardsView.as_view(), name='generate-flashcards'),
    # path('api/documents/<uuid:document_id>/generate/explanations/', GenerateExplanationsView.as_view(), name='generate-explanations'),
    path('api/schema/', SpectacularAPIView.as_view(), name='schema'),
    path('api/docs/', SpectacularSwaggerView.as_view(url_name='schema')),
]

# За development: сервирай media файлове
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
