// API функции за работа с Django бекенда - обновени за новата структура

// Types for the new structure
export interface Course {
  id: string;
  name: string;
  description: string;
  color: string;
  documents_count: number;
  study_sets_count: number;
  created_at: string;
  updated_at: string;
}

export interface Document {
  id: string;
  title: string;
  file: string;
  file_size: number;
  file_size_mb: number;
  file_type: string;
  status: string;
  course: string | null;
  course_name: string | null;
  extracted_text: string;
  ai_summary: string;
  view_count: number;
  study_sessions_count: number;
  created_at: string;
  updated_at: string;
  last_accessed: string | null;
}

export interface StudySet {
  id: string;
  name: string;
  description: string;
  content_type: string;
  document: string;
  document_title: string;
  items_count: number;
  times_studied: number;
  average_score: number | null;
  created_at: string;
  updated_at: string;
}

export interface Flashcard {
  id: string;
  front: string;
  back: string;
  difficulty: string;
  tags: string[];
  ease_factor: number;
  interval_days: number;
  next_review_date: string;
  times_reviewed: number;
  times_correct: number;
  success_rate: number;
  is_due: boolean;
  created_at: string;
  updated_at: string;
}

export interface Quiz {
  id: string;
  title: string;
  description: string;
  time_limit_minutes: number | null;
  shuffle_questions: boolean;
  show_correct_answers: boolean;
  questions: Question[];
  questions_count: number;
  total_points: number;
  times_taken: number;
  average_score: number | null;
  created_at: string;
  updated_at: string;
}

export interface Question {
  id: string;
  question_text: string;
  question_type: string;
  choices: string[];
  correct_choice: string;
  correct_answer: string;
  explanation: string;
  points: number;
  order: number;
  created_at: string;
}

// API Functions

// Courses
export async function getCourses(): Promise<Course[]> {
  const res = await fetch('/api/courses/');
  if (!res.ok) throw new Error('Грешка при зареждане на курсове');
  return await res.json();
}

export async function createCourse(course: Partial<Course>): Promise<Course> {
  const res = await fetch('/api/courses/', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(course),
  });
  if (!res.ok) throw new Error('Грешка при създаване на курс');
  return await res.json();
}

// Documents
export async function uploadDocument(file: File, courseId?: string): Promise<Document> {
  const formData = new FormData();
  formData.append('file', file);
  if (courseId) {
    formData.append('course', courseId);
  }

  const res = await fetch('/api/documents/upload/', {
    method: 'POST',
    body: formData,
  });
  if (!res.ok) {
    const errorData = await res.json();
    throw new Error(errorData.error || 'Грешка при качване');
  }
  return await res.json();
}

export async function getDocuments(courseId?: string): Promise<Document[]> {
  const url = courseId ? `/api/documents/?course=${courseId}` : '/api/documents/';
  const res = await fetch(url);
  if (!res.ok) throw new Error('Грешка при зареждане на документи');
  return await res.json();
}

export async function getDocumentDetails(id: string): Promise<Document> {
  const res = await fetch(`/api/documents/${id}/`);
  if (!res.ok) throw new Error('Документът не е намерен');
  return await res.json();
}

// AI Generation
export async function generateFlashcards(documentId: string): Promise<{study_set_id: string, flashcards_count: number, status: string}> {
  const res = await fetch(`/api/documents/${documentId}/generate/flashcards/`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' }
  });
  if (!res.ok) {
    const errorData = await res.json();
    throw new Error(errorData.error || 'Грешка при генериране на флашкарти');
  }
  return await res.json();
}

export async function generateQuiz(documentId: string): Promise<{study_set_id: string, quiz_id: string, questions_count: number, status: string}> {
  const res = await fetch(`/api/documents/${documentId}/generate/quiz/`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' }
  });
  if (!res.ok) {
    const errorData = await res.json();
    throw new Error(errorData.error || 'Грешка при генериране на тест');
  }
  return await res.json();
}

// Study Sets
export async function getStudySets(documentId?: string): Promise<StudySet[]> {
  const url = documentId ? `/api/study-sets/?document=${documentId}` : '/api/study-sets/';
  const res = await fetch(url);
  if (!res.ok) throw new Error('Грешка при зареждане на study sets');
  return await res.json();
}

export async function getStudySetDetails(id: string): Promise<StudySet> {
  const res = await fetch(`/api/study-sets/${id}/`);
  if (!res.ok) throw new Error('Study set не е намерен');
  return await res.json();
}

// Flashcards
export async function getFlashcards(studySetId: string): Promise<Flashcard[]> {
  const res = await fetch(`/api/study-sets/${studySetId}/flashcards/`);
  if (!res.ok) throw new Error('Грешка при зареждане на флашкарти');
  return await res.json();
}

export async function getFlashcardsDue(): Promise<Flashcard[]> {
  const res = await fetch('/api/flashcards/due/');
  if (!res.ok) throw new Error('Грешка при зареждане на флашкарти за днес');
  return await res.json();
}

// Quizzes
export async function getQuizDetails(id: string): Promise<Quiz> {
  const res = await fetch(`/api/quizzes/${id}/`);
  if (!res.ok) throw new Error('Тестът не е намерен');
  return await res.json();
}

// Statistics
export async function getUserStatistics(): Promise<any> {
  const res = await fetch('/api/stats/');
  if (!res.ok) throw new Error('Грешка при зареждане на статистики');
  return await res.json();
}

// Legacy functions for backward compatibility (deprecated)
export async function getDocumentStatus(id: string) {
  console.warn('getDocumentStatus is deprecated, use getDocumentDetails instead');
  return getDocumentDetails(id);
}

export async function generateQuestions(documentId: string) {
  console.warn('generateQuestions is deprecated, use generateQuiz instead');
  return generateQuiz(documentId);
}

export async function generateExplanations(documentId: string) {
  console.warn('generateExplanations is deprecated - this feature is now part of study sets');
  throw new Error('generateExplanations is no longer supported');
}
  